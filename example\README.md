# Easy Social Share Example

This example app demonstrates how to use the [easy_social_share](../README.md) Flutter plugin to share content to various social media platforms and messaging apps.

## Getting Started

### Prerequisites
- Dart SDK (>=3.0.0 <4.0.0)
- Flutter SDK (>=3.32.4)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/tentram/easy_social_share.git
cd easy_social_share/example
```

2. Install dependencies:
```bash
flutter pub get
```

3. Run the example app:
```bash
flutter run
```

Check the [lib/main.dart](lib/main.dart) codes and play with it.

For more detailed usage, please read the [README](../README.md) file in parent/main directory.

## Dependencies

Required:
- `flutter`: Flutter SDK
- `easy_social_share`: The main plugin (path dependency to parent directory)

For demo purpose:
- `file_picker`: For selecting internal files to share.
- `cached_network_image`: To display and cache external images.
- `flutter_cache_manager`: To get the cached external images to share.
- `cupertino_icons`: iOS-style icons

## Contributing

This example is part of the easy_social_share plugin package. For contributions and issues, please visit the [main repository](https://github.com/tentram/easy_social_share).

## License

This example follows the same license as the main easy_social_share plugin [license](../LICENSE).
