{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "D:/flutter-packages/easy_social_share/example/android/app/.cxx/Debug/3k4t3h1k/x86_64", "source": "C:/Users/<USER>/fvm/versions/stable/packages/flutter_tools/gradle/src/main/scripts"}, "version": {"major": 2, "minor": 3}}